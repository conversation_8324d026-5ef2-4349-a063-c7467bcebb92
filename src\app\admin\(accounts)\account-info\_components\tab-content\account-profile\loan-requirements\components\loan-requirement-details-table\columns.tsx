'use client';

import { ColumnDef } from '@tanstack/react-table';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { RequestStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { cn } from '@/lib/utils';

import { IFarmerLoanRequirementItems, LOAN_REQ_ITEMS } from '../../types/loan-requirements.types';

export const columnLoanRequirementDetails: ColumnDef<IFarmerLoanRequirementItems>[] = [
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Badge
          className={cn('min-w-max', {
            'bg-green-500': data.is_completed === 1,
            'bg-orange-500': data.is_completed === 0,
          })}
        >
          {data.is_completed === 1 ? 'Completed' : 'Pending'}
        </Badge>
      );
    },
    accessorFn: (row) => {
      return row.is_completed === 1 ? 'Completed' : 'Pending';
    },
  },
  {
    id: 'requirements_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Requirements Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{LOAN_REQ_ITEMS[data.name]}</div>;
    },
  },
  /* {
    id: 'transaction_type',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Type" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex">
          <div className={cn('text-white px-4 py-2 text-xs', TransactionTypeLabels[data.type].color)}>
            {TransactionTypeLabels[data.type].label}
          </div>
        </div>
      );
    },
  },
  {
    id: 'date_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${new Date(data.created_at).toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })} | ${new Date(data.created_at).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      const data = row.original;
      return `${new Date(data.created_at).toLocaleDateString('en-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })}`;
    },
  },
  
  {
    id: 'amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${Number(data.amount).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row.total_price}`;
    },
  },
  {
    id: 'loan_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.topupRequest?.reference_number}</div>;
    },
    accessorFn: (row) => {
      return `${row.topupRequest?.reference_number}`;
    },
  },
  {
    id: 'loan_terms',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Terms (in days)" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{`${data.topupRequest?.loan_term ?? 0} days`}</div>;
    },
    accessorFn: (row) => {
      return `${row.topupRequest?.loan_term}`;
    },
  },
  {
    id: 'due_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Due Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${new Date(data.topupRequest?.due_at).toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row.topupRequest?.loan_term}`;
    },
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    accessorFn: (row) => {
      const processedBy = row?.topupRequest?.processedBy ?? {};
      return `${processedBy.finance ? `${processedBy.finance.first_name} ${processedBy.finance.last_name}` : processedBy.email}`;
    },
  }, */
];
